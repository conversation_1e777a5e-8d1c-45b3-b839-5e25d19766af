<template>
  <div id="index" ref="appRef">
    <div class="bg">
      <dv-loading v-if="loading">Loading...</dv-loading>
      <div v-else class="host-body">
        <!--
{{   dateData.dateDay  }}
{{   dateData.dateWeek  }}
{{   dateData.dateYear  }} -->
        <!-- title -->
        <div class="titleText">{{ title }}</div>
        <!-- logo -->
        <div style="position: absolute; top: 50px; left: 44px">
          <img src="@/assets/logoImg.png" alt="" style="width: 174px; height: 40px" />
        </div>
        <!-- center -->
        <div class="centerContent">
          <div class="center_leftTop_title">今年入库单</div>
          <div class="center_leftTop_num">{{ centerNumber.r1 }}</div>
        </div>
        <div class="centerContent" style="left: 1180px">
          <div class="center_leftTop_title">今年出库单</div>
          <div class="center_leftTop_num">{{ centerNumber.r2 }}</div>
        </div>
        <div class="centerContent" style="top: 504px">
          <div class="center_leftTop_title">物资类别（一级）</div>
          <div class="center_leftTop_num">{{ centerNumber.r3 }}</div>
        </div>
        <div class="centerContent" style="left: 1180px; top: 504px">
          <div class="center_leftTop_title">物资目录</div>
          <div class="center_leftTop_num">{{ centerNumber.r4 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, ref, reactive, onMounted, onUnmounted } from 'vue'
import { formatTime } from '@/utils/index'
import { WEEK } from '@/constant/index'
import useDraw from '@/utils/useDraw'
//title
let title = ref('智慧戎仓大数据智控平台')
//leftTop_logo

//rightTop_time
const dateData = reactive<any>({
  dateDay: '',
  dateYear: [],
  dateWeek: '',
  timing: null,
})

const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
const timeFn = () => {
  dateData.timing = setInterval(() => {
    const time = formatTime(new Date(), 'HH:mm:ss')
    dateData.dateDay = time
    const month =
      new Date().getMonth() + 1 < 10
        ? '0' + (new Date().getMonth() + 1)
        : new Date().getMonth() + 1
    const day =
      new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
    const year = new Date().getFullYear() + '.' + month + '.' + day
    dateData.dateYear = year
    //获取星期几
    const week = weekday[new Date().getDay()]
    dateData.dateWeek = week
  }, 16)
}

//center
const centerNumber = reactive({
  r1: '71438',
  r2: '97783',
  r3: '3036',
  r4: '300',
})
// * 颜色
// * 加载标识
const loading = ref<boolean>(true)

// * 适配处理
const { appRef, calcRate, windowDraw, unWindowDraw } = useDraw()
// 生命周期
onMounted(() => {
  timeFn()

  cancelLoading()
  // todo 屏幕适应
  windowDraw()
  calcRate()
})

onUnmounted(() => {
  unWindowDraw()
  clearInterval(dateData.timing)
})

// methods
const cancelLoading = () => {
  setTimeout(() => {
    loading.value = false
  }, 500)
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/index.scss';
// title
.titleText {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  font-family: 'alim';
  font-size: 44px;
  background: linear-gradient(180deg, #a2ffda 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
// center
.centerContent {
  position: absolute;
  top: 174px;
  left: 644px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  .center_leftTop_title {
    width: 130px;
    text-align: center;
    font-family: 'alia';
    font-size: 16px;
    font-style: normal;
    font-weight: 55 Regular;
    line-height: normal;
  }
  .center_leftTop_num {
    text-align: center;
    font-family: 'roboto';
    font-size: 36px;
    font-weight: Condensed SemiBold Italic;
    line-height: normal;
    letter-spacing: 0.72px;
    background: linear-gradient(180deg, #ffcd61 0%, #ffe0a6 75.72%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
