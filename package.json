{"name": "vue-big-screen-plugin", "version": "1.0.1", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "core-js": "^3.6.5", "echarts": "^5.1.1", "vue": "^3.0.0", "vue-class-component": "^8.0.0-0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/cli-plugin-babel": "~5.0.1", "@vue/cli-plugin-eslint": "~5.0.1", "@vue/cli-plugin-router": "~5.0.1", "@vue/cli-plugin-typescript": "~5.0.1", "@vue/cli-plugin-vuex": "~5.0.1", "@vue/cli-service": "~5.0.1", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.10.0", "eslint-plugin-vue": "^8.5.0", "sass": "^1.26.5", "sass-loader": "^12.6.0", "typescript": "~4.6.2"}}